import Combine
import Foundation

public enum HTTPError: Error {
  case invalidResponse
  case unexpectedStatusCode(Int)
  case invalidURL
  case encodingError
  case decodingError(Error)

  public var localizedDescription: String {
    switch self {
    case .invalidResponse:
      return "Invalid response received from the server."
    case .unexpectedStatusCode(let statusCode):
      return "Request failed with status code \(statusCode)."
    case .invalidURL:
      return "Invalid URL or path provided."
    case .encodingError:
      return "Failed to encode data for the request."
    case .decodingError(let error):
      return "Failed to decode response data: \(error.localizedDescription)"
    }
  }
}

public class HTTPClient: @unchecked Sendable {
  private let session: URLSession
  private let baseURLLock = NSLock()
  private var _baseURL: URL
  private let headersLock = NSLock()
  private var _headers: [String: String] = [:]

  public var baseURL: URL {
    baseURLLock.lock()
    defer { baseURLLock.unlock() }
    return _baseURL
  }

  var headers: [String: String] {
    headersLock.lock()
    defer { headersLock.unlock() }
    return _headers
  }

  /// Initializes the HTTPClient with a base URL string.
  /// - Parameter baseURL: The base URL string. Defaults to "http://localhost:8000".
  /// - Note: Crashes on initialization if the base URL string is invalid, as the client cannot function without it.
  public init(baseURL: String = "http://localhost:8000") {
    guard let url = URL(string: baseURL) else {
      fatalError("HTTPClient Initialization Error: Invalid base URL string: \(baseURL)")
    }
    self._baseURL = url
    session = URLSession.shared  // Using shared session for basic use cases.
  }

  /// Sets a new base URL for the client.
  /// - Parameter urlString: The new base URL string.
  /// - Throws: HTTPError.invalidURL if the provided string is not a valid URL.
  public func setBaseURL(_ urlString: String) throws {
    guard let url = URL(string: urlString) else {
      throw HTTPError.invalidURL
    }
    baseURLLock.lock()
    defer { baseURLLock.unlock() }
    self._baseURL = url
    // Removed UserDefaults integration here as it's outside the client's core responsibility.
  }

  /// Sets the Authorization header with a Bearer token.
  /// - Parameter token: The Bearer token string.
  public func setBearerToken(_ token: String) {
    headersLock.lock()
    defer { headersLock.unlock() }
    _headers["Authorization"] = "Bearer \(token)"
  }

  /// Removes the Authorization header.
  public func removeBearerToken() {
    headersLock.lock()
    defer { headersLock.unlock() }
    _headers.removeValue(forKey: "Authorization")
  }

  /// Sets a custom API key header.
  /// - Parameter key: The API key string.
  public func setApiKey(_ key: String) {
    headersLock.lock()
    defer { headersLock.unlock() }
    _headers["ApiKey"] = key
  }

  // MARK: - Public Request Methods

  /// Performs a GET request with optional parameters sent as URL query items.
  /// - Parameters:
  ///   - path: The path relative to the base URL.
  ///   - parameters: An optional Encodable struct representing the query parameters.
  /// - Returns: Decodable response data.
  /// - Throws: HTTPError for network, encoding, or decoding failures.
  public func get<Parameters: Encodable, Response: Decodable>(
    path: String,
    parameters: Parameters? = nil  // Optional parameters for query string
  ) async throws -> Response {
    // Convert optional parameters struct into an array of URLQueryItem if parameters are provided.
    let queryItems = try parameters.map { try buildQueryItems(from: $0) }
    // Call the core request function with query items. GET requests should not have a body.
    let data = try await request(path: path, method: "GET", queryItems: queryItems, body: nil)
    // Decode the response data.
    return try decodeResponse(data: data)
  }

  /// Performs a POST request with an optional Encodable body.
  /// - Parameters:
  ///   - path: The path relative to the base URL.
  ///   - body: An optional Encodable struct representing the request body.
  /// - Returns: Decodable response data.
  /// - Throws: HTTPError for network, encoding, or decoding failures.
  public func post<RequestBody: Encodable, Response: Decodable>(
    path: String,
    body: RequestBody? = nil  // Optional request body
  ) async throws -> Response {
    // Encode the optional request body struct into Data if body is provided.
    let requestBody = try body.map { try JSONEncoder().encode($0) }
    // Call the core request function, passing the body data. POST typically doesn't use query items for the main payload.
    let data = try await request(path: path, method: "POST", queryItems: nil, body: requestBody)
    // Decode the response data.
    return try decodeResponse(data: data)
  }

  /// Performs a POST request with an optional Encodable body and no expected response body.
  /// - Parameters:
  ///   - path: The path relative to the base URL.
  ///   - body: An optional Encodable struct representing the request body.
  /// - Throws: HTTPError for network or encoding failures.
  public func postWithoutResponse<RequestBody: Encodable>(
    path: String,
    body: RequestBody? = nil  // Optional request body
  ) async throws {
    // Encode the optional request body struct into Data if body is provided.
    let requestBody = try body.map { try JSONEncoder().encode($0) }
    // Call the core request function, passing the body data. Ignore the returned Data.
    _ = try await request(path: path, method: "POST", queryItems: nil, body: requestBody)
  }

  /// Performs a DELETE request with an optional Encodable body.
  /// Note: Sending bodies with DELETE is less common but supported by some APIs.
  /// - Parameters:
  ///   - path: The path relative to the base URL.
  ///   - body: An optional Encodable struct representing the request body.
  /// - Returns: Raw Data received in the response (as DELETE responses might not always be JSON).
  /// - Throws: HTTPError for network or encoding failures.
  public func delete<RequestBody: Encodable>(
    path: String,
    body: RequestBody? = nil  // Optional request body
  ) async throws -> Data {
    // Encode the optional request body struct into Data if body is provided.
    let requestBody = try body.map { try JSONEncoder().encode($0) }
    // Call the core request function, passing the body data. DELETE typically doesn't use query items for the main payload.
    return try await request(path: path, method: "DELETE", queryItems: nil, body: requestBody)
  }

  // MARK: - Private Core Request Execution

  /// Core method to execute an HTTP request.
  /// Handles URL construction, query items, request body, headers, and basic response validation.
  /// - Parameters:
  ///   - path: The path relative to the base URL.
  ///   - method: The HTTP method (e.g., "GET", "POST").
  ///   - queryItems: An optional array of URLQueryItem for the URL's query string.
  ///   - body: Optional Data for the request's HTTP body.
  /// - Returns: The raw Data received in the response body.
  /// - Throws: HTTPError for network, URL construction, or status code issues.
  private func request(
    path: String,
    method: String,
    queryItems: [URLQueryItem]? = nil,  // Explicitly for URL query parameters
    body: Data? = nil  // Explicitly for request body data
  ) async throws -> Data {
    let url = try buildURL(path: path, queryItems: queryItems)
    var request = URLRequest(url: url)
    request.httpMethod = method

    if let requestBody = body {
      request.httpBody = requestBody
      request.setValue("application/json", forHTTPHeaderField: "Content-Type")
    }

    for (key, value) in headers {
      request.setValue(value, forHTTPHeaderField: key)
    }

    logRequest(request: request)
    let (data, response) = try await session.data(for: request)
    let httpResponse = try validateResponse(response)
    logResponse(data: data, response: httpResponse)

    return try validateStatusCode(data: data, response: httpResponse)
  }

  private func buildURL(path: String, queryItems: [URLQueryItem]?) throws -> URL {
    guard var urlComponents = URLComponents(string: baseURL.absoluteString + path) else {
      throw HTTPError.invalidURL
    }
    urlComponents.queryItems = queryItems
    guard let url = urlComponents.url else {
      throw HTTPError.invalidURL
    }
    return url
  }

  private func validateResponse(_ response: URLResponse) throws -> HTTPURLResponse {
    guard let httpResponse = response as? HTTPURLResponse else {
      throw HTTPError.invalidResponse
    }
    return httpResponse
  }

  private func validateStatusCode(data: Data, response: HTTPURLResponse) throws -> Data {
    switch response.statusCode {
    case 200...299:
      return data
    default:
      print("\nStatus Code: \(response.statusCode)")
      throw HTTPError.unexpectedStatusCode(response.statusCode)
    }
  }

  private func logRequest(request: URLRequest) {
    print("Request: \(request.httpMethod ?? "") \(request.url?.absoluteString ?? "")")
    //    print("Request Headers:")
    //    request.allHTTPHeaderFields?.forEach { key, value in
    //      print("  \(key): \(value)")
    //    }
    if let httpBody = request.httpBody, let bodyString = String(data: httpBody, encoding: .utf8) {
      print("\nRequest Body:")
      print(bodyString)
    } else if request.httpBody != nil {
      print(
        "\nRequest Body: Data exists but cannot be converted to UTF-8 string (\(request.httpBody!.count) bytes)."
      )
    }
  }

  private func logResponse(data: Data, response: HTTPURLResponse) {
    //    print("\nResponse Headers:")
    //    for (key, value) in response.allHeaderFields {
    //      if let keyString = key as? String, let valueString = value as? String {
    //        print("  \(keyString): \(valueString)")
    //      } else {
    //        print("  \(key): \(value)")
    //      }
    //    }

    if let responseString = String(data: data, encoding: .utf8) {
      print("\nResponse Body:")
      print(responseString)
    } else {
      print("\nResponse Body: Unable to convert data to string (\(data.count) bytes)")
    }
  }

  // MARK: - Private Helper Functions

  /// Converts an Encodable object into an array of URLQueryItem suitable for a URL query string.
  /// This involves encoding the Encodable to JSON, then mapping the resulting dictionary structure
  /// into URLQueryItem(name:value:) pairs. Handles basic types and arrays by repeating keys.
  /// - Parameter encodable: The object to convert (typically a struct representing query parameters).
  /// - Returns: An array of URLQueryItem.
  /// - Throws: HTTPError.encodingError if encoding or conversion fails.
  private func buildQueryItems(from encodable: Encodable) throws -> [URLQueryItem] {
    let encoder = JSONEncoder()
    // Add any necessary encoder configuration here (e.g., date encoding strategy).
    // encoder.dateEncodingStrategy = .iso8601

    let data: Data
    do {
      // Encode the Encodable object into Data (usually JSON).
      data = try encoder.encode(encodable)
    } catch {
      // Catch and rethrow with our custom error type.
      throw HTTPError.encodingError
    }

    let dictionary: [String: Any]
    do {
      // Deserialize the Data into a [String: Any] dictionary.
      // .allowFragments allows handling top-level arrays or values, though parameters typically map to a dictionary.
      guard
        let jsonObject = try JSONSerialization.jsonObject(with: data, options: .allowFragments)
          as? [String: Any]
      else {
        // If the encoded object isn't a dictionary, it's not suitable for simple query parameters.
        throw HTTPError.encodingError  // Indicate incompatible structure.
      }
      dictionary = jsonObject
    } catch {
      // Catch and rethrow JSON parsing errors.
      throw HTTPError.encodingError
    }

    var queryItems: [URLQueryItem] = []
    for (key, value) in dictionary {
      // Convert value to string. URLQueryItem handles percent encoding.
      if let array = value as? [Any] {
        // Handle arrays by creating a query item for each element with the same key.
        // This is standard for URL query strings (e.g., ?item=a&item=b).
        queryItems.append(
          contentsOf: array.compactMap { element in
            // Convert each array element to string. compactMap filters out nil results.
            if let stringValue = stringify(element) {
              return URLQueryItem(name: key, value: stringValue)
            }
            return nil  // Skip elements that cannot be stringified.
          })
      } else if !(value is NSNull) {
        // Handle other non-null values.
        if let stringValue = stringify(value) {
          queryItems.append(URLQueryItem(name: key, value: stringValue))
        }
        // Ignore values that cannot be stringified.
      }
      // Ignore NSNull values.
    }
    return queryItems
  }

  /// Helper function to convert various basic types to a String representation for URLQueryItem values.
  /// - Parameter value: The value to convert.
  /// - Returns: A String representation, or nil if conversion is not supported or if the input is NSNull.
  private func stringify(_ value: Any) -> String? {
    switch value {
    case let string as String:
      return string
    case let int as Int:
      return String(int)
    case let double as Double:
      return String(double)
    case let bool as Bool:
      // Represent booleans as "true" or "false".
      return String(bool)
    case let date as Date:
      // Handle Dates using ISO8601 format.
      let formatter = ISO8601DateFormatter()
      return formatter.string(from: date)
    case is NSNull:
      return nil  // Explicitly handle NSNull.
    default:
      // Fallback for other types, acknowledging potential issues.
      print(
        "Warning: Attempted to convert unhandled type \(type(of: value)) to string for query parameter. Using String(describing:)."
      )
      return String(describing: value)
    }
  }

  /// Decodes the response data into the specified Decodable type.
  /// - Parameter data: The Data received from the HTTP response.
  /// - Returns: The decoded object.
  /// - Throws: HTTPError.decodingError if decoding fails.
  private func decodeResponse<Response: Decodable>(data: Data) throws -> Response {
    let decoder = JSONDecoder()
    // Add any necessary decoder configuration here (e.g., date decoding strategy, key decoding strategy).
    // decoder.dateDecodingStrategy = .iso8601
    // decoder.keyDecodingStrategy = .convertFromSnakeCase

    do {
      // Perform the decoding.
      return try decoder.decode(Response.self, from: data)
    } catch {
      // Catch and rethrow decoding errors wrapped in our custom error.
      throw HTTPError.decodingError(error)
    }
  }
}
