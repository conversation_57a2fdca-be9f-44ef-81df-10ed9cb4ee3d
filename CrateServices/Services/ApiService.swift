import Foundation

public protocol ApiServiceProtocol {
  func updateApiKey(_ key: String) async
}

public struct ApiService: ApiServiceProtocol {
  private let client: HTTPClient
  private let userState: UserState

  public init(client: HTTPClient, userState: UserState = UserState.shared) {
    self.client = client
    self.userState = userState

    // Initialize API key asynchronously
    Task {
      await client.setApiKey(UserDefaults.Keys.apiKey)
    }
  }

  public func updateApiKey(_ key: String) async {
    await client.setApiKey(key)
  }
}
